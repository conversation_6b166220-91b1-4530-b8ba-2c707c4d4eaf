use salvo::{conn::TcpListener, handler, Depot, Listener, Request, Response, Router, Server, Writer};

#[tokio::main]
async fn main() {
    tracing_subscriber::fmt().init();

    let acceptor = TcpListener::new("[::]:8080").bind().await;

    let router = Router::new().get(hello);

    println!("{:?}", router);

    Server::new(acceptor).serve(router).await;
}

struct Demo {
    name: String,
}

impl Writer for Demo {
    async fn write(self, req: &mut Request, depot: &mut Depot, res: &mut Response) {
        res.render(format!("name: {}", self.name)).aw   
    }
}

#[handler]
async fn hello() -> Result<Demo, ()> {
    Ok(Demo {
        name: "Gao Le".to_string(),
    })
}
